// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  // return requestClient.post<any[]>('/rgdc-search/tPortalFavorites/listByPage', data);
  return requestClient.post<any[]>(
    '/rgdc-search/tPortalFavorites/listByPage',
    data,
  );
}

export async function saveFavorites(data: any) {
  return requestClient.post<any>('/rgdc-search/tPortalFavorites/save', data);
}

export async function deleteBatch(data: any) {
  return requestClient.delete<any>(
    `/rgdc-search/tPortalFavorites/deleteBatch/${data}`,
  );
}
export async function getOne(data: any) {
  return requestClient.get<any>(`/rgdc-search/tPortalFavorites/getOne/${data}`);
}
export async function getByIds(data: any) {
  return requestClient.get<any>(
    `/rgdc-search/tPortalFavorites/getByIds/${data}`,
  );
}

export async function getOneByOperationCode(data: any) {
  return requestClient.get<any>(
    `/rgdc-search/tPortalFavorites/getOneByOperationCode/${data}`,
  );
}
