<script setup lang="ts">
import { UploadIcon } from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Dialog,
  Form,
  FormItem,
  Input,
  Loading,
  MessagePlugin,
  Space,
  Table,
  Upload,
} from 'tdesign-vue-next';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { createTaskAll, getGatherTasks } from './api';

// 路由
const router = useRouter();

// 响应式数据
const loading = ref(false);
const taskName = ref('');
const doi = ref('');
const fileList = ref<File[]>([]);
const btnStart = ref('开始汇聚');
const dialogVisible = ref(false);
const uploadLists = ref<any[]>([]);

// 表单引用
const formRef = ref();

// 表格列配置
const columns = [
  { colKey: 'timestamp', title: '上传时间', width: 180 },
  { colKey: 'task_name', title: '任务名称' },
  {
    colKey: 'operation',
    title: '操作',
    width: 180,
    cell: (h: any, { row }: any) => {
      return h(
        Button,
        {
          size: 'small',
          onClick: () => handlePreview(row),
        },
        '查看',
      );
    },
  },
];

// 文件上传前处理
const beforeUpload = (file: any) => {
  const isLt500M = file.size / 1024 / 1024 < 500;
  if (!isLt500M) {
    MessagePlugin.error('上传文件大小不能超过 500MB!');
    return false;
  }

  // 检查文件类型
  const allowedTypes = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ];
  if (
    !allowedTypes.includes(file.type) &&
    !file.name.toLowerCase().endsWith('.xlsx') &&
    !file.name.toLowerCase().endsWith('.xls')
  ) {
    MessagePlugin.error('仅支持Excel文件上传!');
    return false;
  }

  fileList.value.push(file.raw);
  MessagePlugin.success('文件上传成功！');
  return false; // 阻止自动上传
};

// 移除文件
const handleRemove = (context: any) => {
  const index = fileList.value.findIndex((f) => f.name === context.file.name);
  if (index !== -1) {
    fileList.value.splice(index, 1);
  }
};

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
};

// 删除文件
const removeFile = (index: number) => {
  fileList.value.splice(index, 1);
  MessagePlugin.success('文件已移除');
};

// 开始汇聚
const getInfoList = async () => {
  if (!doi.value && fileList.value.length === 0) {
    MessagePlugin.error(
      '需要输入一个DOI或者上传至少一个含有DOI数据的EXCEL文件！',
    );
    return;
  }

  if (!taskName.value.trim()) {
    MessagePlugin.warning('请填写任务名称');
    return;
  }

  loading.value = true;
  btnStart.value = '正在汇聚...';

  try {
    const formData = new FormData();

    // 添加文件
    fileList.value.forEach((file) => {
      formData.append('excel_file', file);
    });

    // 添加参数
    formData.append('doi', doi.value);
    formData.append('task_name', taskName.value);

    const response: any = await createTaskAll(formData);

    if (response.code === 0) {
      MessagePlugin.success('汇聚成功！');
      // 跳转到清洗任务页面
      router.push({
        path: '/data-tools-detail/clean-task',
        query: {
          table_name: response.data.table_name,
          task_name: response.data.task_name,
        },
      });
    } else {
      MessagePlugin.error(response.message || '汇聚失败');
    }
  } catch (error) {
    console.error('汇聚失败:', error);
    MessagePlugin.error('汇聚失败，请重试');
  } finally {
    loading.value = false;
    btnStart.value = '开始汇聚';
  }
};

// 打开任务列表
const startopen = async () => {
  dialogVisible.value = true;
  await getTableData();
};

// 获取任务列表
const getTableData = async () => {
  try {
    const params = {
      page: 1,
      page_size: 10,
    };

    const response: any = await getGatherTasks(params);

    if (response.code === 0) {
      uploadLists.value = response.data || [];
    }
  } catch (error) {
    console.error('获取任务列表失败:', error);
  }
};

// 预览任务
const handlePreview = (row: any) => {
  router.push({
    path: '/data-tools-detail/clean-task',
    query: {
      table_name: row.table_name,
      task_name: row.task_name,
    },
  });
};

// 重置表单
const resetForm = () => {
  taskName.value = '';
  doi.value = '';
  fileList.value = [];
};
</script>

<template>
  <div class="data-cleaning-container">
    <Card class="main-card">
      <template #header>
        <div class="card-header">
          <h2 class="page-title">数据汇聚与清洗工具</h2>
        </div>
      </template>

      <div class="description">
        <p class="description-text">
          多来源获取科技文献原始语料数据，能够对获取到的数据进行机器清洗和人工校正补全等操作，并邀请化工领域专家深入参与数据遴选流程，结合领域知识对原始数据进行筛选与评估，剔除无关或低质量数据，确保收集到的语料具有高质量和高相关性。
        </p>
      </div>

      <Loading :loading="loading" show-overlay prevent-scroll-through>
        <div class="content-area">
          <div class="center">
            <div class="form-container">
              <Form
                ref="formRef"
                label-align="left"
                label-width="100px"
                class="main-form"
              >
                <FormItem label="任务名称：" required>
                  <div class="form-row">
                    <Input
                      v-model="taskName"
                      placeholder="请输入任务名称"
                      class="task-input"
                    />
                    <Button
                      theme="success"
                      @click="startopen"
                      class="task-list-btn"
                    >
                      任务列表
                    </Button>
                  </div>
                </FormItem>

                <FormItem label="DOI">
                  <Input
                    v-model="doi"
                    placeholder="请输入doi"
                    class="doi-input"
                  />
                </FormItem>

                <FormItem label="DOI列表">
                  <Upload
                    theme="custom"
                    draggable
                    :before-upload="beforeUpload"
                    :on-remove="handleRemove"
                    action="/query_file_json"
                    accept=".xlsx,.xls"
                    multiple
                    class="upload-area"
                  >
                    <template #dragContent>
                      <div class="upload-trigger">
                        <UploadIcon size="48" />
                        <div class="upload-text">
                          将文件拖到此处，或点击上传
                        </div>
                        <div class="upload-tip">仅支持Excel文件上传</div>
                      </div>
                    </template>
                  </Upload>

                  <!-- 文件列表显示 -->
                  <div v-if="fileList.length > 0" class="file-list">
                    <div
                      v-for="(file, index) in fileList"
                      :key="index"
                      class="file-item"
                    >
                      <div class="file-info">
                        <span class="file-name">{{ file.name }}</span>
                        <span class="file-size">{{
                          formatFileSize(file.size)
                        }}</span>
                      </div>
                      <Button
                        size="small"
                        theme="danger"
                        variant="text"
                        @click="removeFile(index)"
                      >
                        删除
                      </Button>
                    </div>
                  </div>
                </FormItem>

                <FormItem>
                  <div class="action-section">
                    <Space>
                      <Button
                        theme="primary"
                        @click="getInfoList"
                        :loading="loading"
                      >
                        {{ btnStart }}
                      </Button>
                      <Button theme="default" @click="resetForm"> 重置 </Button>
                    </Space>
                  </div>
                </FormItem>
              </Form>
            </div>
          </div>

          <div class="notice-section">
            <div class="download-notice">
              <span class="notice-icon">⚠</span>解构过程中请勿刷新页面
            </div>
          </div>
        </div>
      </Loading>
    </Card>

    <!-- 任务列表对话框 -->
    <Dialog v-model:visible="dialogVisible" title="上传记录" width="60%">
      <Table
        :data="uploadLists"
        :columns="columns"
        bordered
        stripe
        class="task-table"
      />
    </Dialog>
  </div>
</template>

<style scoped lang="less">
.data-cleaning-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.main-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.description {
  margin-bottom: 32px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #0052d9;
}

.description-text {
  margin: 0;
  line-height: 1.6;
  color: #333;
  font-size: 16px;
}

.content-area {
  min-height: 600px;
}

.center {
  border: 1px solid #ccc;
  width: 60%;
  margin: 20px auto;
  border-radius: 20px;
  padding: 30px;
  min-width: 1200px;
  background: #fff;
}

.form-container {
  width: 70%;
  padding-top: 34px;
}

.main-form {
  .t-form__item {
    margin-bottom: 24px;
  }
}

.form-row {
  display: flex;
  align-items: center;
  gap: 60px;
}

.task-input {
  max-width: 500px;
}

.doi-input {
  max-width: 500px;
}

.task-list-btn {
  flex-shrink: 0;
}

.upload-area {
  width: 100%;
  max-width: 660px;
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  border: 2px dashed #dcdcdc;
  border-radius: 12px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 250px;
  text-align: center;

  &:hover {
    border-color: #0052d9;
    background: #f0f7ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 82, 217, 0.1);
  }

  .upload-text {
    margin-top: 20px;
    color: #333;
    font-size: 16px;
    font-weight: 500;
  }

  .upload-tip {
    margin-top: 10px;
    color: #999;
    font-size: 14px;
  }
}

.file-list {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  margin-bottom: 8px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.file-size {
  color: #666;
  font-size: 12px;
}

.action-section {
  text-align: right;
  margin-right: 30px;
}

.notice-section {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.download-notice {
  font-size: 14px;
  color: #666;
  display: inline-block;
  margin-top: 10px;
}

.notice-icon {
  color: #ff9800;
  margin-right: 5px;
}

.task-table {
  margin-top: 16px;
}

// 响应式设计
@media (max-width: 1200px) {
  .center {
    width: 80%;
    min-width: auto;
    padding: 20px;
  }

  .form-container {
    width: 100%;
  }

  .form-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .task-input,
  .doi-input {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .data-cleaning-container {
    padding: 16px;
  }

  .center {
    width: 95%;
    padding: 16px;
  }

  .upload-trigger {
    height: 200px;
    padding: 24px 16px;
  }

  .action-section {
    text-align: center;
    margin-right: 0;
  }
}
</style>
