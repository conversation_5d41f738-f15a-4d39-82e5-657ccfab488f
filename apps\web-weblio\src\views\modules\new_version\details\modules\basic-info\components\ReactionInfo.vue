<script setup lang="ts">
import {
  Button,
  Table,
  Dialog
} from 'tdesign-vue-next';
import { computed, onMounted, reactive, ref } from 'vue';

import {
  getReactionInfoData,
  getReactStepInfo,
  getReferenceInfo
} from '../api';
import { useSearchStore } from '#/store/search';

interface ReactionInfo {
  id: string;
  createdBy: string;
  createTime: string;
  updatedBy: string;
  updateTime: string;
  isDeleted: string;
  reactionId: string;
  unique: string;
  reactionSmiles: string;
  reactant: string;
  product: string;
  reactionType: string;
  reactionTime: string;
  reactionTemperature: string;
  reactionPressure: string;
  reactionPh: string;
  otherConditions: string;
  reactionYield: string;
  reagent: string;
  catalyst: string;
  solvent: string;
  docUnique: string;
}
/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
  * 加载状态
  */
  loading: true,

});
// 响应式数据
const reactList = ref<ReactionInfo[][]>([]);
const stepList = ref<any>(null);
const reference = ref<any>(null);
// 实验步骤Dialog
const showStepDialog = ref(false);
// 文献Dialog
const showReferenceDialog = ref(false);
// 控制显示全部的状态
const showAll = ref(false);
const displayedReactList = computed(() => {
  return showAll.value ? reactList.value : reactList.value.slice(0, 2);
});
// 表格列定义
const getTableColumns = () => [
  {
    colKey: 'reactionYield',
    title: '产率',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'reactionTemperature',
    title: '温度',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'reactionTime',
    title: '时间',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'reactionPressure',
    title: '压力',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'reactionPh',
    title: 'Ph',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'solvent',
    title: '溶剂',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'reagent',
    title: '试剂',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'catalyst',
    title: '催化剂',
    width: 80,
    ellipsis: true,
  },
  {
    colKey: 'otherConditions',
    title: '其他条件',
    width: 80,
    ellipsis: true,
  },
  {
    title: '实验步骤',
    width: 80,
    cell: (h: any, { row }: { row: any }) => h('span', {
      style: 'color: #3480fe; text-decoration: underline; cursor: pointer',
      onClick: () => openStepDialog(row) // 传入当前行数据
    }, '查看')
  },
  {
    title: '文献',
    width: 80,
    cell: (h: any, { row }: { row: any }) => h('span', {
      style: 'color: #3480fe; text-decoration: underline; cursor: pointer',
      onClick: () => openReferenceDialog(row) // 传入当前行数据
    }, '查看')
  },
];
// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    console.log("当前详情数据", detail, 2222);
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    console.log("当前详情数据", local, 2222);
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

const openStepDialog = async (rowData: any) => {
  // 调用实验步骤查询API
  const response = await getReactStepInfo({ unique: rowData.unique });
  stepList.value = response;
  showStepDialog.value = true;
};

const openReferenceDialog = async (rowData: any) => {
  // 调用反应文献查询API
  const response = await getReferenceInfo({ docUnique: rowData.docUnique });
  reference.value = response;
  showReferenceDialog.value = true;
};

const initReactionInfo = async (flag: String) => {
  try {
    // 设置显示全部状态,隐藏更多按钮
    showAll.value = flag == 'more';
    // 调用反应信息查询API
    const response = await getReactionInfoData({ inchikey: state.detailItem.baseCode, isMore: flag });
    reactList.value = response;
  } catch (error) {
    console.error('获取反应信息失败:', error);
  } finally {
    // 关闭加载状态
    state.loading = false;
  }
};

onMounted(async () => {
  await initReactionInfo('');
});
</script>

<template>
  <div class="substance-react-info">
    <div v-if="state.loading" class="loading-container">
      <div class="loading-spinner">加载反应信息中...</div>
    </div>

    <template v-else>
      <!-- 反应信息区域 -->
      <div class="react-section">

        <!-- 遍历每个反应显示对应的表格 -->
        <div v-for="(react, index) in reactList" class="react-table-container">
          <h1 class="font-style">#{{ index + 1 }}</h1>
          <div class="table-content">
            <h3>{{ react[0] ? react[0].reactionSmiles : '' }}</h3>
            <h3 class="font-style2">反应详情</h3>
            <!-- 反应产品表格 -->
            <div class="react-table">
              <Table :data="react" :columns="getTableColumns()" :loading="state.loading" :bordered="true" :hover="true"
                :stripe="true" row-key="id" table-layout="fixed" cell-empty-content="-" />
            </div>
          </div>
        </div>

        <div v-if="!showAll" class="show-more-container">
          <Button theme="primary" @click="initReactionInfo('more')">
            查看更多
          </Button>
        </div>

        <div v-if="reactList.length === 0" class="empty-state">
          <p>暂无反应信息</p>
        </div>
      </div>
    </template>
  </div>
  <Dialog v-model:visible="showStepDialog" header="实验步骤" :footer="false" width="50vw">
    <!-- 实验步骤内容 -->
    <div class="dialog-content-scrollable">
      <div v-for="step in stepList" class="step-item">
        <p>步骤名称: <span class="title-text" v-html="step.exampleTitle"></span></p>
        <p>步骤描述: <span class="title-text" v-html="step.fulltextReaction"></span></p>
      </div>
    </div>
  </Dialog>

  <Dialog v-model:visible="showReferenceDialog" header="反应文献" :footer="false" width="50vw">
    <!-- 实验步骤内容 -->
    <div class="dialog-content-scrollable">
      <div class="reference-item">
        <p><span class="title-text" v-html="reference?.title"></span></p>
        <p>摘要: <span class="title-text" v-html="reference?.docAbstract"></span></p>
      </div>
    </div>
  </Dialog>
</template>

<style scoped lang="scss">
.substance-react-info {
  padding: 20px;

  // 加载状态样式
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;

    .loading-spinner {
      font-size: 16px;
      color: #666;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        right: -20px;
        top: 50%;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        border: 2px solid #e8e8e8;
        border-top: 2px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }

    @keyframes spin {
      0% {
        transform: translateY(-50%) rotate(0deg);
      }

      100% {
        transform: translateY(-50%) rotate(360deg);
      }
    }
  }

  .font-style {
    font-size: 20px;
    font-weight: bold;
  }

  .font-style2 {
    font-size: 16px;
    color: rgba(108, 108, 108, 1);
    padding: 10px 0 5px 0;
  }

  .react-table-container {
    border: 2px solid #e8e8e8;
    margin-bottom: 20px;
    overflow: hidden;
    padding: 20px;
  }

  .table-content {
    padding: 10px 20px;
  }

  .show-more-container {
    display: flex;
    justify-content: center;
    margin-top: 16px;
  }
}

.dialog-content-scrollable {
  min-height: 50vh;
  max-height: 50vh;
  overflow-y: auto; // 垂直滚动
  padding-right: 8px; // 避免滚动条遮挡内容

  .step-item,
  .reference-item {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    p {
      margin: 8px 0;
      /* 调整段落间距 */
      line-height: 1.6;
      /* 增加行高 */
    }

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
  }

  .title-text {
    font-weight: 600;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f8f8f8;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #b8b8b8;
    border-radius: 10px;
    border: 1px solid #f8f8f8;

    &:hover {
      background-color: #909090;
    }
  }
}
</style>
